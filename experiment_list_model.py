from PySide6.QtCore import QAbstractListModel, Qt, QModelIndex, Signal, Slot, Property
import json

class ExperimentListModel(QAbstractListModel):
    countChanged = Signal()

    ExpNumRole = Qt.ItemDataRole.UserRole + 1
    StartDateRole = Qt.ItemDataRole.UserRole + 2
    EndDateRole = Qt.ItemDataRole.UserRole + 3
    AtomicMassRole = Qt.ItemDataRole.UserRole + 4
    AtomicNumberRole = Qt.ItemDataRole.UserRole + 5
    IonSrcRole = Qt.ItemDataRole.UserRole + 6
    TargetRole = Qt.ItemDataRole.UserRole + 7
    Q1Role = Qt.ItemDataRole.UserRole + 8
    Q2Role = Qt.ItemDataRole.UserRole + 9
    Q3Role = Qt.ItemDataRole.UserRole + 10
    MaxEnergyRole = Qt.ItemDataRole.UserRole + 11
    ChargeToMassRole = Qt.ItemDataRole.UserRole + 12
    RigidityRole = Qt.ItemDataRole.UserRole + 13

    def __init__(self, parent=None):
        super().__init__(parent)
        self.experiments = []
    
    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        row = index.row()
        if row < 0 or row >= len(self.experiments):
            return None

        role_to_key = {
            self.ExpNumRole: "expNum",
            self.StartDateRole: "startDate",
            self.EndDateRole: "endDate",
            self.AtomicMassRole: "a",
            self.AtomicNumberRole: "z",
            self.IonSrcRole: "ionSrc",
            self.TargetRole: "target",
            self.Q1Role: "q1",
            self.Q2Role: "q2",
            self.Q3Role: "q3",
            self.MaxEnergyRole: "maxEnergy",
            self.ChargeToMassRole: "chargeMassRatio",
            self.RigidityRole: "rigidity"
        }

        key = role_to_key.get(role)
        if key:
            return self.experiments[row].get(key)
        return None

    def rowCount(self, parent=QModelIndex()):
        return len(self.experiments)
    
    @Property(int, notify=countChanged) # type: ignore
    def count(self):
        return len(self.experiments)
    
    def roleNames(self):
        return {
            self.ExpNumRole: b"expNum",
            self.StartDateRole: b"startDate",
            self.EndDateRole: b"endDate",
            self.AtomicMassRole: b"atomicMass",
            self.AtomicNumberRole: b"atomicNumber",
            self.IonSrcRole: b"ionSrc",
            self.TargetRole: b"target",
            self.Q1Role: b"q1",
            self.Q2Role: b"q2",
            self.Q3Role: b"q3",
            self.MaxEnergyRole: b"maxEnergy",
            self.ChargeToMassRole: b"chargeMassRatio",
            self.RigidityRole: b"rigidity"
        }

    @Slot(int)
    def set_exp_count(self, exp_count):
        self.exp_count = exp_count
    
    def add_experiment(self, experiment):
        self.beginInsertRows(QModelIndex(), len(self.experiments), len(self.experiments))
        self.experiments.append(experiment)
        self.endInsertRows()
        self.countChanged.emit()

    @Slot(str)
    def populate_experiment_list(self, json_str):
        if (len(self.experiments) > self.exp_count):
            self.clear()

        try:
            exp_data = json.loads(json_str)
            self.add_experiment(exp_data)
        except Exception as e:
            print("Failed to parse experiment list: ", e)
    
    @Slot()
    def clear(self):
        self.beginResetModel()
        self.experiments.clear()
        self.endResetModel()
        self.countChanged.emit()