from PySide6.QtCore import Slot, QObject, Signal
import asyncio
import threading
import json
from websockets.asyncio.client import connect

background_loop = None

def run_event_loop():
    global background_loop
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    background_loop = loop
    loop.run_forever()

loop_thread = threading.Thread(target=run_event_loop, daemon=True)
loop_thread.start()

class WebsocketClient(QObject):
    commentFetched = Signal(str)
    beamlinePathFetched = Signal(str)

    def __init__(self):
        super().__init__()

    @Slot(str)
    def select_exp_num(self, expNum, run_in_background=True):
        self.selected_exp_num = expNum

        if background_loop and run_in_background:
            asyncio.run_coroutine_threadsafe(self.fetch_comment_and_beamline_data(), background_loop)
        else:
            asyncio.create_task(self.fetch_comment_and_beamline_data())

    async def fetch_comment_and_beamline_data(self):
        try:
            async with connect("ws://127.0.0.1/ws?expnum=" + self.selected_exp_num + "&has_comments=True", port=8080, max_size=None) as websoc:
                async for message in websoc:
                    try:
                        data = json.loads(message)
                        with open('output.txt', 'w') as file:
                            json.dump(data, file, indent=2)
                        if "comments" in data:
                            json_str = json.dumps(data)
                            self.commentFetched.emit(json_str)
                        if "beamline" in data:
                            json_str = json.dumps(data)
                            self.beamlinePathFetched.emit(json_str)
                    except json.JSONDecodeError:
                        print("Received non-JSON message:", message)
        except Exception as e:
            print("WebSocket connection failed:", e)
