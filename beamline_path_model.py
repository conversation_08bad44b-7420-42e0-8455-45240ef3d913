from PySide6.QtCore import QAbstractListModel, Qt, QModelIndex, Slot
import json

class BeamlinePathModel(QAbstractListModel):
    ImgPathRole = Qt.ItemDataRole.UserRole + 1
    TimestampRole = Qt.ItemDataRole.UserRole + 2
    DeviceNameRole = Qt.ItemDataRole.UserRole + 3
    DeviceTypeRole = Qt.ItemDataRole.UserRole + 4
    ChannelNamesRole = Qt.ItemDataRole.UserRole + 5
    ValuesRole = Qt.ItemDataRole.UserRole + 6

    def __init__(self, parent=None):
        super().__init__(parent)
        self.devices = []
        self.all_devices = []

        # Track active filters
        self.active_device_type = None
        self.active_device_types = None  # NEW
        self.active_device_name = None
        self.active_timestamp = None

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        row = index.row()
        if not (0 <= row < len(self.devices)):
            return None

        role_to_key = {
            self.ImgPathRole: "img_path",
            self.TimestampRole: "timestamp",
            self.DeviceNameRole: "device_name",
            self.DeviceTypeRole: "device_type",
            self.ChannelNamesRole: "channel_names",
            self.ValuesRole: "values",
        }

        key = role_to_key.get(role)
        return self.devices[row].get(key) if key else None

    def rowCount(self, parent=QModelIndex()):
        return len(self.devices)

    def roleNames(self):
        return {
            self.ImgPathRole: b"imgPath",
            self.TimestampRole: b"timestamp",
            self.DeviceNameRole: b"deviceName",
            self.DeviceTypeRole: b"deviceType",
            self.ChannelNamesRole: b"channelNames",
            self.ValuesRole: b"values"
        }

    def get_device_type(self, device_type, device_id):
        device_img_mapping = {
            "MAGNET": "dipole",
            "STEERER": "steerer",
            "QUAD_SINGLET": "quad_singlet_magnetic",
            "QUAD_DOUBLET": "quad_doublet_electrostatic" if device_id < 50000 else "quad_doublet_magnetic",
            "QUAD_TRIPLET": "quad_triplet_electrostatic" if device_id < 50000 else "quad_triplet_magnetic",
            "SOLENOID_SOA": "solenoid",
            "SOLENOID": "solenoid",
            "RESONATOR": "resonator"
        }
        return device_img_mapping.get(device_type, "")

    def _apply_filters(self):
        filtered = self.all_devices

        if self.active_device_types:
            filtered = [d for d in filtered if d["device_type"] in self.active_device_types]
        elif self.active_device_type:
            filtered = [d for d in filtered if d["device_type"] == self.active_device_type]

        if self.active_device_name:
            filtered = [d for d in filtered if d["device_name"] == self.active_device_name]

        if self.active_timestamp is not None:
            filtered = [d for d in filtered if d["timestamp"] == self.active_timestamp]

        self.devices = filtered

    @Slot(str)
    def filter_by_type(self, device_type=None):
        self.beginResetModel()
        self.active_device_type = device_type
        self.active_device_types = None
        self._apply_filters()
        self.endResetModel()

    @Slot(list)
    def filter_by_types(self, device_types):
        self.beginResetModel()
        self.active_device_types = set(device_types) if device_types else None
        self.active_device_type = None
        self._apply_filters()
        self.endResetModel()

    @Slot(str)
    def filter_by_name(self, device_name=None):
        self.beginResetModel()
        self.active_device_name = device_name
        self._apply_filters()
        self.endResetModel()

    @Slot(float)
    def filter_by_timestamp(self, timestamp):
        self.beginResetModel()
        self.active_timestamp = timestamp
        self._apply_filters()
        self.endResetModel()

    @Slot()
    def clear_all_filters(self):
        self.beginResetModel()
        self.active_device_type = None
        self.active_device_types = None
        self.active_device_name = None
        self.active_timestamp = None
        self.devices = self.all_devices
        self.endResetModel()

    @Slot(str)
    def populate_beamline_path(self, json_str):
        data = json.loads(json_str)
        device_list = []

        with open("beamline_output.txt", "w") as f:
            try:
                for _, device_entry in data["beamline"].items():
                    for device_name, channels in device_entry.items():
                        print("Device name: ", device_name, file=f)
                        device_dict = {
                            "device_name": device_name,
                            "timestamp": 0,
                            "device_type": "",
                            "channel_names": [],
                            "values": []
                        }
                        for channel_name, channel_info in channels.items():
                            if channel_info["_value"] != "DNE":
                                print("Device ID: ", channel_info["device_id"], file=f)
                                try:
                                    print("Measurement: ", channel_info["_measurement"], file=f)
                                    print("Value: ", channel_info["_value"], file=f)

                                    try:
                                        device_type = self.get_device_type(channel_info["typeName"], channel_info["device_id"])
                                        device_dict["img_path"] = f"qrc:/assets/device_images/{device_type}.svg"
                                        device_dict["timestamp"] = channel_info["_time"]
                                        device_dict["device_type"] = device_type
                                        device_dict["channel_names"].append(channel_name.split(':')[-1])
                                        device_dict["values"].append(channel_info["_value"])
                                    except KeyError:
                                        print("No device type name or device id found.")
                                except KeyError:
                                    print("No measurement found.")
                        if channel_info["_value"] != "DNE":
                            device_list.append(device_dict)
                        print("\n", file=f)
            except KeyError:
                print("Warning: 'beamline' key not found in JSON")

        self.beginResetModel()
        self.devices = device_list
        self.all_devices = device_list

        self.active_device_type = None
        self.active_device_types = None
        self.active_device_name = None
        self.active_timestamp = None

        self.endResetModel()