from PySide6.QtCore import Slot, QObject, Signal
import httpx
import threading
import asyncio
import json
from datetime import datetime, timezone
from qasync import asyncSlot
from bs4 import BeautifulSoup
from bs4.element import Tag
from typing import cast

def extract_clean_html(rich_text: str) -> str:
    soup = BeautifulSoup(rich_text, "html.parser")
    body = soup.body

    if not isinstance(body, Tag):
        return ""

    lines = []
    for p in body.find_all("p"):
        p_tag = cast(Tag, p)
        line_html = p_tag.decode_contents().strip()
        if line_html:  # skip empty lines
            lines.append(line_html)

    return "<br/>".join(lines)

class Server(QObject):
    experimentCount = Signal(int)
    experimentFetched = Signal(str)

    def __init__(self):
        super().__init__()
        self._loop = asyncio.new_event_loop()
        threading.Thread(target=self._start_loop, daemon=True).start()
    
    def _start_loop(self):
        asyncio.set_event_loop(self._loop)
        self._loop.run_forever()
    
    async def _get_experiment_data(self, query=""):
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"http://127.0.0.1:8080/experiments?{query}")
                response.raise_for_status()
                data = response.json()
                self.experimentCount.emit(len(data) - 1)
                for experiment in data:
                    exp_data = data[experiment]
                    json_str = json.dumps(exp_data)
                    self.experimentFetched.emit(json_str)
            except httpx.HTTPStatusError as status_err:
                print("HTTP Status Error", status_err)
            except httpx.HTTPError as http_err:
                #print(http_err)
                print("HTTP Error", http_err)
    
    @Slot(str)
    def get_experiment_data(self, query):
        asyncio.run_coroutine_threadsafe(self._get_experiment_data(query=query), self._loop)

    @asyncSlot(str, float, str)
    async def post_comment(self, exp_num, timestamp, comment):
        print(f"Experiment #: {exp_num}")
        print(f"Timestamp: {timestamp}")

        dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
        zulu_timestamp = dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        comment_raw = comment
        cleaned_comment = extract_clean_html(comment_raw)
        print(f"Comment: {cleaned_comment}")

        comment_json = {
            "expNum": exp_num,
            "timestamp": zulu_timestamp,
            "comment": cleaned_comment
        }

        async with httpx.AsyncClient() as client:
            try:
                response = await client.post("http://127.0.0.1:8080/comment", headers=headers, json=comment_json)
                response.raise_for_status()
                print(response.text)
            except httpx.HTTPStatusError as status_err:
                print("HTTP Status Error", status_err)
            except httpx.HTTPError as http_err:
                print(f"HTTP Error", http_err)