from PySide6.QtCore import QAbstractListModel, Qt, QModelIndex, Slot
import json

class CommentListModel(QAbstractListModel):
    TimestampRole = Qt.ItemDataRole.UserRole + 1
    CommentRole = Qt.ItemDataRole.UserRole + 2

    def __init__(self, parent=None):
        super().__init__(parent)
        self.comments = []

    def data(self, index, role=Qt.ItemDataRole.DisplayRole):
        row = index.row()
        if not (0 <= row < len(self.comments)):
            return None

        role_to_key = {
            self.TimestampRole: "timestamp",
            self.CommentRole: "comment",
        }

        key = role_to_key.get(role)
        if key:
            return self.comments[row].get(key, "")

        return None

    def rowCount(self, parent=QModelIndex()):
        return len(self.comments)

    def roleNames(self):
        return {
            self.TimestampRole: b"timestamp",
            self.CommentRole: b"comment"
        }
    
    @Slot(str)
    def add_comment(self, comment_str):
        try:
            comment = json.loads(comment_str)
            self.beginInsertRows(QModelIndex(), len(self.comments), len(self.comments))
            self.comments.append(comment)
            self.endInsertRows()
        except Exception as e:
            print("Failed to add comment:", e)

    @Slot(str)
    def filter_by_keyword(self, keyword):
        self.beginResetModel()
        if keyword != "":
            self.comments = [comment for comment in self.comments if keyword.casefold() in comment["comment"].casefold()]
        else:
            self.comments = self.all_comments
        self.endResetModel()

    @Slot(str)
    def populate_comment_list(self, json_str):
        try:
            data = json.loads(json_str)
            try:
                comment_dict = data["comments"]
                self.beginResetModel()
                self.comments = list(comment_dict.values())
                self.all_comments = self.comments
                self.endResetModel()
                print(f"Loaded {len(self.comments)} comments")
            except KeyError:
                print("Warning: 'comments' key not found in JSON")
        except Exception as e:
            print("Failed to parse comment list:", e)