pragma ComponentBehavior: Bound

import QtQuick
import QtQuick.Controls.Basic

ComboBox {
    id: root
    
    property string labelText: ""
    property var options: []
    
    // Set the model to the provided options
    model: root.options
    
    delegate: ItemDelegate {
        id: delegate
        required property var model
        required property var index
        width: root.width
        
        MouseArea {
            id: hoverArea
            anchors.fill: parent
            hoverEnabled: true
            acceptedButtons: Qt.NoButton
        }
        
        contentItem: Text {
            text: delegate.model[root.textRole]
            color: "white"
            font: root.font
            elide: Text.ElideRight
            verticalAlignment: Text.AlignVCenter
        }
        
        background: Rectangle {
            color: hoverArea.containsMouse ? "#3e424b" : "#393939"
        }
        
        highlighted: root.highlightedIndex == index
    }
    
    indicator: Canvas {
        id: canvas
        x: root.width - width - root.rightPadding
        y: root.topPadding + (root.availableHeight - height) / 2
        width: 12
        height: 8
        contextType: "2d"
        
        Connections {
            target: root
            function onPressedChanged() {
                canvas.requestPaint();
            }
        }
        
        onPaint: {
            var ctx = getContext("2d");
            if (ctx) {
                ctx.reset();
                ctx.moveTo(0, 0);
                ctx.lineTo(width, 0);
                ctx.lineTo(width / 2, height);
                ctx.closePath();
                ctx.fillStyle = root.pressed ? "#48aaad" : "white";
                ctx.fill();
            }
        }
    }
    
    contentItem: Text {
        leftPadding: 10
        rightPadding: root.indicator.width + root.spacing
        
        text: root.displayText
        font: root.font
        color: root.activeFocus ? "#48aaad" : "white"
        verticalAlignment: Text.AlignVCenter
        elide: Text.ElideRight
    }
    
    background: Rectangle {
        color: "#393939"
        implicitWidth: 120
        implicitHeight: 40
        border.color: root.pressed ? "#48aaad" : "white"
        border.width: root.visualFocus ? 2 : 1
        radius: 2
    }
    
    popup: Popup {
        y: root.height - 1
        width: root.width
        height: Math.min(contentItem.implicitHeight, root.Window.height - topMargin - bottomMargin)
        implicitHeight: contentItem.implicitHeight
        padding: 1
        
        background: Rectangle {
            color: "#393939"
            implicitWidth: 120
            implicitHeight: 40
            border.color: root.pressed ? "#48aaad" : "white"
            border.width: root.visualFocus ? 2 : 1
            radius: 2
        }
        
        contentItem: ListView {
            clip: true
            implicitHeight: contentHeight
            model: root.popup.visible ? root.delegateModel : null
            currentIndex: root.highlightedIndex
            
            ScrollIndicator.vertical: ScrollIndicator {}
        }
    }
}
