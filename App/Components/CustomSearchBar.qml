pragma ComponentBehavior: Bound

import QtQuick
import QtQuick.Controls.Basic

TextField {
    id: root
    
    property string placeholderTextString: ""
    property color backgroundColor: "#3e424b"
    property color borderColor: "#666666"
    property color focusBorderColor: "#48aaad"
    property color textColor: "white"
    property color placeholderColor: "#999999"
    property int borderRadius: 4
    property bool showBorder: true
    
    // Signals for different search actions
    signal searchTriggered(string searchText)
    signal textCleared()
    
    placeholderText: qsTr(root.placeholderTextString)
    placeholderTextColor: root.placeholderColor
    color: root.textColor
    
    background: Rectangle {
        color: root.backgroundColor
        radius: root.borderRadius
        border.color: root.showBorder ? (root.activeFocus ? root.focusBorderColor : root.borderColor) : "transparent"
        border.width: root.showBorder ? 1 : 0
    }
    
    Keys.onReturnPressed: {
        root.searchTriggered(root.text);
    }
    
    onTextChanged: {
        if (text.length === 0) {
            root.textCleared();
        }
    }
    
    // Helper function to clear the search
    function clearSearch() {
        root.text = "";
        root.textCleared();
    }
    
    // Helper function to set search text programmatically
    function setSearchText(searchText) {
        root.text = searchText;
        root.searchTriggered(searchText);
    }
}
