pragma ComponentBehavior: Bound

import QtQuick
import QtQuick.Layouts
import QtQuick.Controls.Basic

RowLayout {
    id: root
    
    property string labelText: ""
    property real fromValue: 0
    property real toValue: 100
    property real firstValue: 0.25
    property real secondValue: 0.75
    property real stepSize: 1
    property int decimalPlaces: 0
    
    // Expose the slider values for external access
    readonly property real firstSliderValue: slider.first.value
    readonly property real secondSliderValue: slider.second.value
    
    Text {
        text: qsTr(root.labelText)
        color: "white"
        Layout.preferredWidth: 100
    }
    
    RangeSlider {
        id: slider
        Layout.fillWidth: true
        Layout.bottomMargin: 20
        from: root.fromValue
        to: root.toValue
        first.value: root.firstValue
        second.value: root.secondValue
        stepSize: root.stepSize
        
        background: Rectangle {
            x: slider.leftPadding
            y: slider.topPadding + slider.availableHeight / 2 - height / 2
            implicitWidth: 200
            implicitHeight: 4
            width: slider.availableWidth
            height: implicitHeight
            radius: 2
            color: "#bdbebf"
            
            Rectangle {
                x: slider.first.visualPosition * parent.width
                width: slider.second.visualPosition * parent.width - x
                height: parent.height
                color: "#48aaad"
                radius: 2
            }
        }
        
        first.handle: Rectangle {
            x: slider.leftPadding + slider.first.visualPosition * (slider.availableWidth - width)
            y: slider.topPadding + slider.availableHeight / 2 - height / 2
            implicitWidth: 26
            implicitHeight: 26
            radius: 13
            color: slider.first.pressed ? "#f0f0f0" : "#f6f6f6"
            border.color: "#bdbebf"
            
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: parent.bottom
                anchors.topMargin: 5
                text: slider.first.value.toFixed(root.decimalPlaces)
                color: "white"
                font.pixelSize: 10
                horizontalAlignment: Text.AlignHCenter
            }
        }
        
        second.handle: Rectangle {
            x: slider.leftPadding + slider.second.visualPosition * (slider.availableWidth - width)
            y: slider.topPadding + slider.availableHeight / 2 - height / 2
            implicitWidth: 26
            implicitHeight: 26
            radius: 13
            color: slider.second.pressed ? "#f0f0f0" : "#f6f6f6"
            border.color: "#bdbebf"
            
            Text {
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.top: parent.bottom
                anchors.topMargin: 5
                text: slider.second.value.toFixed(root.decimalPlaces)
                color: "white"
                font.pixelSize: 10
                horizontalAlignment: Text.AlignHCenter
            }
        }
    }
}
