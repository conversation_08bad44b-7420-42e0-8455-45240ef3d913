pragma ComponentBehavior: Bound

import QtQuick
import QtQuick.Controls.Basic
// qmllint disable unqualified

Rectangle {
    id: container
    width: parent.width
    height: parent.height
    color: "#3e424b"

    required property var selectedExperiment

    function formatChannelValues(channels, values) {
        var result = "";
        for (var i = 0; i < channels.length; ++i) {
            var name = channels[i];
            var val = values[i];
            result += "<span style='color:white'>" + name + "</span> " + "<span style='color:#48aaad'>" + val + "</span><br/>";
        }
        return result;
    }

    ScrollView {
        width: parent.width
        height: parent.height
        anchors.bottom: container.bottom
        visible: container.selectedExperiment
        clip: true
        ScrollBar.horizontal.policy: ScrollBar.AlwaysOn

        contentItem: ListView {
            id: beamlinePath
            width: parent.width
            visible: container.selectedExperiment
            orientation: ListView.Horizontal
            spacing: 50
            leftMargin: 200
            rightMargin: 200
            boundsBehavior: Flickable.StopAtBounds
            snapMode: ListView.SnapToItem
            flickDeceleration: 2000
            highlightFollowsCurrentItem: false
            model: beamlinePathModel

            delegate: Item {
                id: beamlineDevice
                width: 200
                height: 50

                required property string imgPath
                required property real timestamp
                required property string deviceName
                required property var channelNames
                required property var values

                Column {
                    spacing: 5
                    width: beamlineDevice.width
                    anchors.horizontalCenter: parent.horizontalCenter

                    Image {
                        source: beamlineDevice.imgPath
                        fillMode: Image.PreserveAspectFit
                        width: beamlineDevice.width
                        height: 50
                    }

                    Column {
                        Text {
                            text: beamlineDevice.deviceName
                            color: "white"
                            font.pixelSize: 24
                            font.bold: true
                            horizontalAlignment: Text.AlignHCenter
                            width: beamlineDevice.width
                            wrapMode: Text.WordWrap
                        }

                        Text {
                            text: Qt.formatDateTime(new Date(beamlineDevice.timestamp * 1000), "MM/dd/yyyy hh:mm:ss")
                            color: "darkseagreen"
                            font.pixelSize: 14
                            horizontalAlignment: Text.AlignHCenter
                            width: beamlineDevice.width
                            wrapMode: Text.WordWrap
                        }

                        Text {
                            textFormat: Text.RichText
                            text: container.formatChannelValues(beamlineDevice.channelNames, beamlineDevice.values)
                            font.pixelSize: 14
                            horizontalAlignment: Text.AlignHCenter
                            width: beamlineDevice.width
                            wrapMode: Text.WordWrap
                        }
                    }
                }
            }
        }
    }
}
