// qmllint disable unqualified

import QtQuick
import QtQuick.Controls.Basic
import QtQuick.Layouts

Rectangle {
    id: container
    width: parent.width * 0.4
    height: parent.height
    color: "#393939"

    signal selectExperiment(var experimentData)

    ExperimentListFilterMenu {
        id: experimentListFilterMenu
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 10

        RowLayout {
            CustomSearchBar {
                id: experimentSearchBar
                Layout.fillWidth: true
                Layout.preferredHeight: implicitHeight
                placeholderTextString: "Search by parameter (e.g. ionSrc=ECR1)"
                borderRadius: 5

                onSearchTriggered: function(searchText) {
                    experimentListModel.clear();

                    if (searchText !== "") {
                        var resultSortingParam = paramsDropdown.getSortingQuery();
                        server.get_experiment_data(searchText + "&" + resultSortingParam);
                    } else {
                        paramsDropdown.fetchSortedExperiments();
                    }
                }

                onTextCleared: {
                    paramsDropdown.fetchSortedExperiments();
                }
            }

            Button {
                id: showExperimentFiltersBtn
                Layout.preferredHeight: 40
                Layout.minimumWidth: contentItem.implicitWidth + 20
                text: qsTr("Show Filters")

                contentItem: Row {
                    spacing: 8
                    anchors.centerIn: parent

                    Text {
                        text: "☰"  // Menu icon as text
                        color: "white"
                        font.pixelSize: 16
                        anchors.verticalCenter: parent.verticalCenter
                    }

                    Text {
                        text: showExperimentFiltersBtn.text
                        color: "white"
                        font: showExperimentFiltersBtn.font
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }

                background: Rectangle {
                    color: "#343434"
                    radius: 4
                    border.color: showExperimentFiltersBtn.hovered ? "#48aaad" : "transparent"
                    border.width: 1
                }

                onClicked: {
                    experimentListFilterMenu.open();
                }
            }
        }

        CustomComboBox {
            id: paramsDropdown
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            options: [
                qsTr("Start Date (Most Recent)"),
                qsTr("End Date (Most Recent)"),
                qsTr("Atomic Mass Ascending"),
                qsTr("Atomic Mass Descending"),
                qsTr("Atomic Number Ascending"),
                qsTr("Atomic Number Descending"),
                qsTr("Ion Source"),
                qsTr("Target"),
                qsTr("q1"),
                qsTr("q2"),
                qsTr("q3"),
                qsTr("Max Energy Ascending"),
                qsTr("Max Energy Descending"),
                qsTr("Charge-to-Mass Ratio Ascending"),
                qsTr("Charge-to-Mass Ratio Descending"),
                qsTr("Rigidity Ascending"),
                qsTr("Rigidity Descending")
            ]

            function getSortingQuery() {
                var sortingQuery = "";

                if (currentValue === qsTr("Atomic Mass Ascending")) {
                    sortingQuery = "orderBy=a";
                } else if (currentValue === qsTr("Atomic Mass Descending")) {
                    sortingQuery = "orderBy=a&desc=true";
                } else if (currentValue === qsTr("Atomic Number Ascending")) {
                    sortingQuery = "orderBy=z";
                } else if (currentValue === qsTr("Atomic Number Descending")) {
                    sortingQuery = "orderBy=z&desc=true";
                } else if (currentValue === qsTr("Ion Source")) {
                    sortingQuery = "orderBy=ionSrc";
                } else if (currentValue === qsTr("Target")) {
                    sortingQuery = "orderBy=target";
                } else if (currentValue === qsTr("q1")) {
                    sortingQuery = "orderBy=q1";
                } else if (currentValue === qsTr("q2")) {
                    sortingQuery = "orderBy=q2";
                } else if (currentValue === qsTr("q3")) {
                    sortingQuery = "orderBy=q3";
                } else if (currentValue === qsTr("End Date (Most Recent)")) {
                    sortingQuery = "orderBy=endDate&desc=true";
                } else if (currentValue === qsTr("Start Date (Most Recent)")) {
                    sortingQuery = "orderBy=startDate&desc=true";
                } else if (currentValue === qsTr("Max Energy Ascending")) {
                    sortingQuery = "orderBy=maxEnergy";
                } else if (currentValue === qsTr("Max Energy Descending")) {
                    sortingQuery = "orderBy=maxEnergy&desc=true";
                } else if (currentValue === qsTr("Charge-to-Mass Ratio Ascending")) {
                    sortingQuery = "orderBy=chargeMassRatio";
                } else if (currentValue === qsTr("Charge-to-Mass Ratio Descending")) {
                    sortingQuery = "orderBy=chargeMassRatio&desc=true";
                } else if (currentValue === qsTr("Rigidity Ascending")) {
                    sortingQuery = "orderBy=rigidity";
                } else if (currentValue === qsTr("Rigidity Descending")) {
                    sortingQuery = "orderBy=rigidity&desc=true";
                }

                return sortingQuery;
            }

            function fetchSortedExperiments() {
                experimentListModel.clear();

                var sortingQuery = getSortingQuery();
                server.get_experiment_data(sortingQuery);
            }

            onActivated: {
                fetchSortedExperiments();
            }
        }

        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            background: Rectangle {
                color: "#393939"
            }
            ScrollBar.vertical.policy: ScrollBar.AlwaysOn

            Component.onCompleted: server.get_experiment_data("orderBy=startDate&desc=true")

            contentItem: ListView {
                id: experimentListView
                clip: true
                boundsBehavior: Flickable.StopAtBounds
                model: experimentListModel

                property int selectedIndex: -1

                delegate: ItemDelegate {
                    id: experimentListItem
                    width: ListView.view.width

                    required property string expNum
                    required property real startDate
                    required property real endDate
                    required property var atomicMass
                    required property var atomicNumber
                    required property string ionSrc
                    required property string target
                    required property real q1
                    required property real q2
                    required property real q3
                    required property real maxEnergy
                    required property real chargeMassRatio
                    required property real rigidity

                    required property int index

                    text: {
                        var selection = paramsDropdown.displayText;
                        var infoToShow = qsTr("Exp #") + expNum;

                        if (selection === qsTr("Start Date (Most Recent)")) {
                            infoToShow += qsTr(" | Start Date: ") + Qt.formatDateTime(new Date(startDate * 1000), "MM/dd/yyyy");
                        } else if (selection === qsTr("End Date (Most Recent)")) {
                            infoToShow += qsTr(" | End Date: ") + Qt.formatDateTime(new Date(endDate * 1000), "MM/dd/yyyy");
                        } else if (selection === qsTr("Atomic Mass Ascending") || selection === qsTr("Atomic Mass Descending")) {
                            infoToShow += qsTr(" | Atomic Mass: ") + atomicMass;
                        } else if (selection === qsTr("Atomic Number Ascending") || selection === qsTr("Atomic Number Descending")) {
                            infoToShow += qsTr(" | Atomic Number: ") + atomicNumber;
                        } else if (selection === qsTr("Ion Source")) {
                            infoToShow += qsTr(" | Ion Source: ") + ionSrc;
                        } else if (selection === qsTr("Target")) {
                            infoToShow += qsTr(" | Target: ") + target;
                        } else if (selection === qsTr("q1")) {
                            infoToShow += qsTr(" | q1: ") + q1;
                        } else if (selection === qsTr("q2")) {
                            infoToShow += qsTr(" | q2: ") + q2;
                        } else if (selection === qsTr("q3")) {
                            infoToShow += qsTr(" | q3: ") + q3;
                        } else if (selection === qsTr("Max Energy Ascending") || selection === qsTr("Max Energy Descending")) {
                            infoToShow += qsTr(" | Max Energy: ") + maxEnergy;
                        } else if (selection === qsTr("Charge-to-Mass Ratio Ascending") || selection === qsTr("Charge-to-Mass Ratio Descending")) {
                            infoToShow += qsTr(" | Charge-to-Mass Ratio: ") + chargeMassRatio;
                        } else if (selection === qsTr("Rigidity Ascending") || selection === qsTr("Rigidity Descending")) {
                            infoToShow += qsTr(" | Rigidity: ") + rigidity;
                        }

                        return infoToShow;
                    }

                    onClicked: {
                        experimentListView.selectedIndex = index;
                        container.selectExperiment({
                            expNum: expNum,
                            startDate: startDate,
                            endDate: endDate,
                            atomicMass: atomicMass,
                            atomicNumber: atomicNumber,
                            ionSrc: ionSrc,
                            target: target,
                            q1: q1,
                            q2: q2,
                            q3: q3,
                            maxEnergy: maxEnergy,
                            chargeMassRatio: chargeMassRatio,
                            rigidity: rigidity
                        });
                    }

                    contentItem: Text {
                        text: experimentListItem.text
                        color: "white"
                        font.pixelSize: 14
                        elide: Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignLeft
                        leftPadding: 16
                        rightPadding: 16
                    }

                    background: Rectangle {
                        color: experimentListView.selectedIndex === experimentListItem.index ? "#48aaad" : "#393939"
                    }
                }

                Text {
                    id: noResultsText
                    x: parent.width / 2 - width / 2
                    y: parent.height / 2 - height / 2
                    text: qsTr("No results")
                    color: "white"
                    font.pixelSize: 16
                    visible: experimentListModel.count == 0
                }
            }
        }
    }
}
