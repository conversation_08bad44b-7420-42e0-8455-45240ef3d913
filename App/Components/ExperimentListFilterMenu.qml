pragma ComponentBehavior: Bound

import QtQuick
import QtQuick.Layouts
import QtQuick.Controls.Basic
import QtQuick.Controls as Controls

Popup {
    id: popup
    width: parent.width
    height: parent.height
    modal: true
    focus: true
    closePolicy: Popup.NoAutoClose

    background: Rectangle {
        color: "#393939"
    }

    Button {
        id: closeButton
        text: qsTr("✕")
        font.pixelSize: 16
        width: 40
        height: 40
        anchors.top: parent.top
        anchors.right: parent.right
        anchors.margins: 15
        onClicked: {
            popup.close();
        }

        background: Rectangle {
            color: "#4c4c4c"
            radius: 4
        }

        contentItem: Text {
            text: closeButton.text
            color: "white"
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            anchors.fill: parent
        }

        ToolTip.text: qsTr("Close")
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 15
        spacing: 10

        Text {
            text: qsTr("Experiment Filters")
            color: "white"
            font.pixelSize: 16
            font.bold: true
        }

        RowLayout {
            Layout.fillWidth: true
            spacing: 10

            Button {
                id: prevButton
                text: qsTr("◀")
                enabled: !(calendarGrid.year === 1997 && calendarGrid.month === 0)
                onClicked: {
                    if (calendarGrid.month === 0) {
                        calendarGrid.month = 11;
                        calendarGrid.year--;
                    } else {
                        calendarGrid.month--;
                    }
                }

                background: Rectangle {
                    color: prevButton.enabled ? (prevButton.down ? "#5a5a5a" : "#4c4c4c") : "#2a2a2a"
                    radius: 4
                }

                contentItem: Text {
                    text: prevButton.text
                    color: prevButton.enabled ? "white" : "#666666"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }

            Text {
                Layout.fillWidth: true
                text: Qt.locale().monthName(calendarGrid.month) + " " + calendarGrid.year
                color: "white"
                font.pixelSize: 14
                horizontalAlignment: Text.AlignHCenter
            }

            Button {
                id: nextButton
                text: qsTr("▶")
                enabled: !(calendarGrid.year === new Date().getFullYear() && calendarGrid.month === new Date().getMonth())
                onClicked: {
                    if (calendarGrid.month === 11) {
                        calendarGrid.month = 0;
                        calendarGrid.year++;
                    } else {
                        calendarGrid.month++;
                    }
                }

                background: Rectangle {
                    color: nextButton.enabled ? (nextButton.down ? "#5a5a5a" : "#4c4c4c") : "#2a2a2a"
                    radius: 4
                }

                contentItem: Text {
                    text: nextButton.text
                    color: nextButton.enabled ? "white" : "#666666"
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
            }
        }

        Controls.MonthGrid {
            id: calendarGrid
            Layout.fillWidth: true
            Layout.preferredHeight: 200
            month: new Date().getMonth()
            year: new Date().getFullYear()
            locale: Qt.locale()

            property date selectedStartDate: new Date()
            property date selectedEndDate: new Date()
            property bool selectingStartDate: true

            delegate: Rectangle {
                id: dayDelegate
                required property date date
                required property int day
                required property int month
                required property bool today

                width: 30
                height: 30
                color: {
                    if (dayDelegate.today)
                        return "#48aaad";
                    if (isInSelectedRange())
                        return "#6bb6b9";
                    if (isSelectedDate())
                        return "#48aaad";
                    return "transparent";
                }
                border.color: dayDelegate.today ? "#ffffff" : "transparent"
                border.width: 1
                radius: 4

                function isSelectedDate() {
                    return (dayDelegate.date.getTime() === calendarGrid.selectedStartDate.getTime() || dayDelegate.date.getTime() === calendarGrid.selectedEndDate.getTime());
                }

                function isInSelectedRange() {
                    var start = calendarGrid.selectedStartDate.getTime();
                    var end = calendarGrid.selectedEndDate.getTime();
                    var current = dayDelegate.date.getTime();
                    return current > Math.min(start, end) && current < Math.max(start, end);
                }

                Text {
                    anchors.centerIn: parent
                    text: dayDelegate.day
                    color: {
                        if (dayDelegate.month !== calendarGrid.month)
                            return "#666666";
                        if (parent.isSelectedDate() || parent.isInSelectedRange() || dayDelegate.today)
                            return "white";
                        return "#cccccc";
                    }
                    font.pixelSize: 12
                    font.bold: dayDelegate.today
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        if (calendarGrid.selectingStartDate) {
                            calendarGrid.selectedStartDate = dayDelegate.date;
                            calendarGrid.selectedEndDate = dayDelegate.date;
                            calendarGrid.selectingStartDate = false;
                        } else {
                            calendarGrid.selectedEndDate = dayDelegate.date;
                            calendarGrid.selectingStartDate = true;
                        }
                    }
                }
            }
        }

        RowLayout {
            Text {
                text: qsTr("Atomic Number")
                color: "white"
            }

            RangeSlider {
                id: atomicNumberSlider
                Layout.fillWidth: true
                first.value: 0.25
                second.value: 0.75

                background: Rectangle {
                    x: atomicNumberSlider.leftPadding
                    y: atomicNumberSlider.topPadding + atomicNumberSlider.availableHeight / 2 - height / 2
                    implicitWidth: 200
                    implicitHeight: 4
                    width: atomicNumberSlider.availableWidth
                    height: implicitHeight
                    radius: 2
                    color: "#bdbebf"

                    Rectangle {
                        x: atomicNumberSlider.first.visualPosition * parent.width
                        width: atomicNumberSlider.second.visualPosition * parent.width - x
                        height: parent.height
                        color: "#48aaad"
                        radius: 2
                    }
                }

                first.handle: Rectangle {
                    x: atomicNumberSlider.leftPadding + atomicNumberSlider.first.visualPosition * (atomicNumberSlider.availableWidth - width)
                    y: atomicNumberSlider.topPadding + atomicNumberSlider.availableHeight / 2 - height / 2
                    implicitWidth: 26
                    implicitHeight: 26
                    radius: 13
                    color: atomicNumberSlider.first.pressed ? "#f0f0f0" : "#f6f6f6"
                    border.color: "#bdbebf"
                }

                second.handle: Rectangle {
                    x: atomicNumberSlider.leftPadding + atomicNumberSlider.second.visualPosition * (atomicNumberSlider.availableWidth - width)
                    y: atomicNumberSlider.topPadding + atomicNumberSlider.availableHeight / 2 - height / 2
                    implicitWidth: 26
                    implicitHeight: 26
                    radius: 13
                    color: atomicNumberSlider.second.pressed ? "#f0f0f0" : "#f6f6f6"
                    border.color: "#bdbebf"
                }
            }
        }

        RowLayout {
            Text {
                text: qsTr("Atomic Mass")
                color: "white"
            }

            RangeSlider {
                id: atomicMassSlider
                Layout.fillWidth: true
                first.value: 0.25
                second.value: 0.75

                background: Rectangle {
                    x: atomicMassSlider.leftPadding
                    y: atomicMassSlider.topPadding + atomicMassSlider.availableHeight / 2 - height / 2
                    implicitWidth: 200
                    implicitHeight: 4
                    width: atomicMassSlider.availableWidth
                    height: implicitHeight
                    radius: 2
                    color: "#bdbebf"

                    Rectangle {
                        x: atomicMassSlider.first.visualPosition * parent.width
                        width: atomicMassSlider.second.visualPosition * parent.width - x
                        height: parent.height
                        color: "#48aaad"
                        radius: 2
                    }
                }

                first.handle: Rectangle {
                    x: atomicMassSlider.leftPadding + atomicMassSlider.first.visualPosition * (atomicMassSlider.availableWidth - width)
                    y: atomicMassSlider.topPadding + atomicMassSlider.availableHeight / 2 - height / 2
                    implicitWidth: 26
                    implicitHeight: 26
                    radius: 13
                    color: atomicMassSlider.first.pressed ? "#f0f0f0" : "#f6f6f6"
                    border.color: "#bdbebf"
                }

                second.handle: Rectangle {
                    x: atomicMassSlider.leftPadding + atomicMassSlider.second.visualPosition * (atomicMassSlider.availableWidth - width)
                    y: atomicMassSlider.topPadding + atomicMassSlider.availableHeight / 2 - height / 2
                    implicitWidth: 26
                    implicitHeight: 26
                    radius: 13
                    color: atomicMassSlider.second.pressed ? "#f0f0f0" : "#f6f6f6"
                    border.color: "#bdbebf"
                }
            }
        }

        RowLayout {
            Text {
                text: qsTr("Atomic Masss")
                color: "white"
            }

            RangeSlider {
                id: atomicMassSlider
                Layout.fillWidth: true
                first.value: 0.25
                second.value: 0.75

                background: Rectangle {
                    x: atomicMassSlider.leftPadding
                    y: atomicMassSlider.topPadding + atomicMassSlider.availableHeight / 2 - height / 2
                    implicitWidth: 200
                    implicitHeight: 4
                    width: atomicMassSlider.availableWidth
                    height: implicitHeight
                    radius: 2
                    color: "#bdbebf"

                    Rectangle {
                        x: atomicMassSlider.first.visualPosition * parent.width
                        width: atomicMassSlider.second.visualPosition * parent.width - x
                        height: parent.height
                        color: "#48aaad"
                        radius: 2
                    }
                }

                first.handle: Rectangle {
                    x: atomicMassSlider.leftPadding + atomicMassSlider.first.visualPosition * (atomicMassSlider.availableWidth - width)
                    y: atomicMassSlider.topPadding + atomicMassSlider.availableHeight / 2 - height / 2
                    implicitWidth: 26
                    implicitHeight: 26
                    radius: 13
                    color: atomicMassSlider.first.pressed ? "#f0f0f0" : "#f6f6f6"
                    border.color: "#bdbebf"
                }

                second.handle: Rectangle {
                    x: atomicMassSlider.leftPadding + atomicMassSlider.second.visualPosition * (atomicMassSlider.availableWidth - width)
                    y: atomicMassSlider.topPadding + atomicMassSlider.availableHeight / 2 - height / 2
                    implicitWidth: 26
                    implicitHeight: 26
                    radius: 13
                    color: atomicMassSlider.second.pressed ? "#f0f0f0" : "#f6f6f6"
                    border.color: "#bdbebf"
                }
            }
        }
    }
}
