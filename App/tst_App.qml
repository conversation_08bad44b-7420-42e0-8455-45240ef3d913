import QtQuick
import QtTest
import "Components"

Item {
    id: testRoot
    width: 0.6 * Screen.width
    height: 0.8 * Screen.height

    property var initialExperiment: ({
            expNum: "527B",
            startDate: 854517600,
            endDate: 854690400,
            atomicMass: 84,
            atomicNumber: 36,
            ionSrc: "ECR1",
            target: "A3",
            q1: 42,
            q2: 42,
            q3: 42
        })

    AppBar {
        id: testAppBar
        SignalSpy {
            id: showDrawerSpy
            target: testAppBar
            signalName: "showDrawer"
        }
    }

    TestCase {
        name: "AppBar toggles drawer"
        when: windowShown

        function test_toggle_drawer() {
            compare(showDrawerSpy.count, 0);
            testAppBar.showDrawer();
            compare(showDrawerSpy.count, 1);
        }
    }

    ExperimentViewer {
        id: testExperimentViewer
        selectedExperiment: testRoot.initialExperiment

        TestCase {
            name: "ExperimentViewer displays the experiment that was selected from the list"
            when: windowShown

            function test_select_experiment_display() {
                compare(testExperimentViewer.selectedExperiment.expNum, "5290-42");
                compare(testExperimentViewer.selectedExperiment.q1, 42);
                compare(testExperimentViewer.selectedExperiment.ionSrc, "NIS");
            }
        }
    }

    Connections {
        target: testExperimentList
        function onSelectExperiment(experimentData) {
            testExperimentViewer.selectedExperiment = experimentData;
        }
    }

    ExperimentList {
        id: testExperimentList
        SignalSpy {
            id: selectExperimentSpy
            target: testExperimentList
            signalName: "selectExperiment"
        }

        TestCase {
            name: "User can select an experiment from the list"
            when: windowShown

            function test_select_experiment() {
                compare(selectExperimentSpy.count, 0);

                var testExperiment = {
                    expNum: "5290-42",
                    startDate: 853308000,
                    endDate: 853740000,
                    atomicMass: 42,
                    atomicNumber: 42,
                    ionSrc: "NIS",
                    target: "A3",
                    q1: 42,
                    q2: 42,
                    q3: 42
                };

                testExperimentList.selectExperiment(testExperiment);
                compare(selectExperimentSpy.count, 1);

                var args = selectExperimentSpy.signalArguments[0];
                verify(args.length > 0);
                compare(args[0].expNum, "5290-42");
                compare(args[0].startDate, 853308000);
                compare(args[0].endDate, 853740000);
                compare(args[0].atomicMass, 42);
                compare(args[0].atomicNumber, 42);
                compare(args[0].ionSrc, "NIS");
                compare(args[0].target, "A3");
                compare(args[0].q1, 42);
                compare(args[0].q2, 42);
                compare(args[0].q3, 42);
            }
        }
    }
}
