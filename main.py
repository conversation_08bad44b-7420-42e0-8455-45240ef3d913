import sys
import os
from PySide6.QtGui import <PERSON><PERSON><PERSON>Application
from PySide6.QtQml import QQmlApplicationEngine
from qasync import Q<PERSON><PERSON><PERSON>oop
import asyncio

from server import Server
from websocket_client import WebsocketClient
from experiment_list_model import ExperimentListModel
from comment_list_model import CommentListModel
from beamline_path_model import BeamlinePathModel

import App.resources_rc

if __name__ == "__main__":
    app = QGuiApplication(sys.argv)
    engine = QQmlApplicationEngine()

    loop = QEventLoop(app)
    asyncio.set_event_loop(loop)

    base_dir = os.path.dirname(__file__)

    experiment_list_model = ExperimentListModel()
    comment_list_model = CommentListModel()
    beamline_path_model = BeamlinePathModel()
    server = Server()
    websocket_client = WebsocketClient()

    engine.rootContext().setContextProperty("experimentListModel", experiment_list_model)
    engine.rootContext().setContextProperty("commentListModel", comment_list_model)
    engine.rootContext().setContextProperty("beamlinePathModel", beamline_path_model)
    engine.rootContext().setContextProperty("server", server)
    engine.rootContext().setContextProperty("websocketClient", websocket_client)

    engine.addImportPath(os.path.join(base_dir, "App", "Components"))
    engine.addImportPath(os.path.join(base_dir, "App"))

    qml_file = os.path.join(base_dir, "App", "Main.qml")
    engine.load(qml_file)

    if not engine.rootObjects():
        sys.exit(-1)

    #exit_code = app.exec()
    #del engine
    #sys.exit(exit_code)

    with loop:
        loop.run_forever()